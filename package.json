{"name": "promptpilot-desktop", "version": "1.0.0", "description": "AI-powered prompt management and enhancement desktop application", "main": "dist/main/main.js", "author": "PromptPilot Team", "license": "MIT", "private": true, "homepage": ".", "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "webpack --config webpack.main.config.js --mode development --watch", "dev:renderer": "webpack serve --config webpack.renderer.config.js --mode development", "build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js --mode production", "build:renderer": "webpack --config webpack.renderer.config.js --mode production", "start": "electron dist/main/main.js", "start:dev": "wait-on dist/main/main.js && electron dist/main/main.js --dev", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "package": "electron-builder", "package:dir": "electron-builder --dir", "package:win": "electron-builder --win", "package:mac": "electron-builder --mac", "package:linux": "electron-builder --linux"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "concurrently": "^8.2.2", "css-loader": "^6.8.1", "electron": "^27.1.2", "electron-builder": "^24.6.4", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.7.0", "prettier": "^3.0.3", "rimraf": "^5.0.5", "style-loader": "^3.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.0", "typescript": "^5.2.2", "wait-on": "^7.2.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "dependencies": {"better-sqlite3": "^9.1.1", "electron-store": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.1", "zustand": "^4.4.6"}, "build": {"appId": "com.promptpilot.desktop", "productName": "PromptPilot Desktop", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "target": "dmg"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "engines": {"node": ">=18.0.0"}}